#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结果显示面板模块
Results Display Panel Module
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                            QTableWidgetItem, QLabel, QPushButton, QGroupBox,
                            QTextEdit, QTabWidget)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from ..geometry.chute_geometry import ChuteGeometry
from ..calculation.engineering_calc import EngineeringCalculator
from ..core.config import AppConfig


class ResultsPanel(QWidget):
    """结果显示面板类"""
    
    def __init__(self):
        """初始化结果面板"""
        super().__init__()
        self.config = AppConfig()
        self.current_results = {}
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("计算结果")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 主体计算结果标签页
        self.create_main_results_tab()
        
        # 法兰计算结果标签页
        self.create_flange_results_tab()
        
        # 详细信息标签页
        self.create_details_tab()
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.calculate_btn = QPushButton("重新计算")
        self.calculate_btn.clicked.connect(self.recalculate)
        button_layout.addWidget(self.calculate_btn)
        
        self.export_btn = QPushButton("导出结果")
        self.export_btn.clicked.connect(self.export_results)
        button_layout.addWidget(self.export_btn)
        
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
    def create_main_results_tab(self):
        """创建主体结果标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 主体工程量表格
        self.main_table = QTableWidget(0, 2)
        self.main_table.setHorizontalHeaderLabels(["项目", "数值"])
        self.main_table.horizontalHeader().setStretchLastSection(True)
        
        layout.addWidget(self.main_table)
        
        self.tab_widget.addTab(tab, "主体工程量")
        
    def create_flange_results_tab(self):
        """创建法兰结果标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 法兰工程量表格
        self.flange_table = QTableWidget(0, 2)
        self.flange_table.setHorizontalHeaderLabels(["项目", "数值"])
        self.flange_table.horizontalHeader().setStretchLastSection(True)
        
        layout.addWidget(self.flange_table)
        
        self.tab_widget.addTab(tab, "法兰工程量")
        
    def create_details_tab(self):
        """创建详细信息标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 详细信息文本框
        self.details_text = QTextEdit()
        self.details_text.setReadOnly(True)
        layout.addWidget(self.details_text)
        
        self.tab_widget.addTab(tab, "详细信息")
        
    def update_results(self, parameters_dict):
        """更新计算结果"""
        try:
            parameters = parameters_dict.get('parameters')
            if not parameters:
                return
                
            # 生成几何体
            geometry_generator = ChuteGeometry(parameters)
            geometry_data = geometry_generator.generate_geometry()
            
            # 计算工程量
            calculator = EngineeringCalculator(parameters, geometry_data)
            results = calculator.calculate_all()
            
            self.current_results = results
            
            # 更新各个标签页
            self.update_main_results_table(results)
            self.update_flange_results_table(results)
            self.update_details_text(parameters, results)
            
        except Exception as e:
            print(f"结果更新错误: {e}")
            self.clear_results()
            
    def update_main_results_table(self, results):
        """更新主体结果表格"""
        self.main_table.setRowCount(0)
        
        # 主体工程量数据
        main_data = [
            ("总表面积", f"{results.get('total_surface_area', 0):.3f} m²"),
            ("侧面积", f"{results.get('side_surface_area', 0):.3f} m²"),
            ("进口面积", f"{results.get('inlet_area', 0):.3f} m²"),
            ("出口面积", f"{results.get('outlet_area', 0):.3f} m²"),
            ("主体体积", f"{results.get('main_body_volume', 0):.6f} m³"),
            ("主体重量", f"{results.get('main_body_weight', 0):.2f} kg"),
        ]
        
        for i, (item, value) in enumerate(main_data):
            self.main_table.insertRow(i)
            self.main_table.setItem(i, 0, QTableWidgetItem(item))
            self.main_table.setItem(i, 1, QTableWidgetItem(value))
            
    def update_flange_results_table(self, results):
        """更新法兰结果表格"""
        self.flange_table.setRowCount(0)
        
        # 法兰工程量数据
        flange_data = [
            ("进口法兰长度", f"{results.get('inlet_flange_length', 0):.3f} m"),
            ("进口法兰重量", f"{results.get('inlet_flange_weight', 0):.2f} kg"),
            ("出口法兰长度", f"{results.get('outlet_flange_length', 0):.3f} m"),
            ("出口法兰重量", f"{results.get('outlet_flange_weight', 0):.2f} kg"),
            ("总重量", f"{results.get('total_weight', 0):.2f} kg"),
        ]
        
        for i, (item, value) in enumerate(flange_data):
            self.flange_table.insertRow(i)
            self.flange_table.setItem(i, 0, QTableWidgetItem(item))
            self.flange_table.setItem(i, 1, QTableWidgetItem(value))
            
    def update_details_text(self, parameters, results):
        """更新详细信息文本"""
        details = []
        
        # 参数信息
        details.append("=== 溜子参数 ===")
        details.append(f"类型: {self.config.CHUTE_TYPES.get(parameters.chute_type, '未知')}")
        details.append(f"高度: {parameters.height:.3f} m")
        details.append(f"板厚: {parameters.plate_thickness:.4f} m")
        details.append(f"材料: {self.config.MATERIALS.get(parameters.material, {}).get('name', '未知')}")
        details.append("")
        
        # 进口参数
        details.append("=== 进口参数 ===")
        details.append(f"形状: {'方形' if parameters.inlet_shape == 'square' else '圆形'}")
        if parameters.inlet_shape == "square":
            details.append(f"宽度: {parameters.inlet_width:.3f} m")
            details.append(f"长度: {parameters.inlet_length:.3f} m")
        else:
            details.append(f"直径: {parameters.inlet_diameter:.3f} m")
        details.append(f"法兰类型: {self.config.FLANGE_TYPES.get(parameters.inlet_flange_type, '未知')}")
        if parameters.inlet_flange_spec:
            details.append(f"法兰规格: {parameters.inlet_flange_spec}")
        details.append("")
        
        # 出口参数
        details.append("=== 出口参数 ===")
        details.append(f"形状: {'方形' if parameters.outlet_shape == 'square' else '圆形'}")
        if parameters.outlet_shape == "square":
            details.append(f"宽度: {parameters.outlet_width:.3f} m")
            details.append(f"长度: {parameters.outlet_length:.3f} m")
        else:
            details.append(f"直径: {parameters.outlet_diameter:.3f} m")
        details.append(f"法兰类型: {self.config.FLANGE_TYPES.get(parameters.outlet_flange_type, '未知')}")
        if parameters.outlet_flange_spec:
            details.append(f"法兰规格: {parameters.outlet_flange_spec}")
        details.append("")
        
        # 偏移参数
        if parameters.offset_x != 0 or parameters.offset_y != 0:
            details.append("=== 偏移参数 ===")
            details.append(f"X偏移: {parameters.offset_x:.3f} m")
            details.append(f"Y偏移: {parameters.offset_y:.3f} m")
            details.append("")
        
        # 计算结果汇总
        details.append("=== 计算结果汇总 ===")
        details.append(f"主体重量: {results.get('main_body_weight', 0):.2f} kg")
        details.append(f"法兰重量: {results.get('inlet_flange_weight', 0) + results.get('outlet_flange_weight', 0):.2f} kg")
        details.append(f"总重量: {results.get('total_weight', 0):.2f} kg")
        details.append("")
        
        # 材料密度信息
        material_info = self.config.MATERIALS.get(parameters.material, {})
        if material_info:
            details.append(f"材料密度: {material_info.get('density', 0)} kg/m³")
        
        self.details_text.setPlainText("\n".join(details))
        
    def clear_results(self):
        """清除结果"""
        self.main_table.setRowCount(0)
        self.flange_table.setRowCount(0)
        self.details_text.clear()
        self.current_results = {}
        
    def recalculate(self):
        """重新计算（需要从主窗口触发）"""
        # 这个方法会被主窗口的信号连接
        pass
        
    def export_results(self):
        """导出结果"""
        # TODO: 实现结果导出功能
        print("导出结果功能待实现")
        
    def get_summary_text(self):
        """获取结果摘要文本"""
        if not self.current_results:
            return "暂无计算结果"
            
        summary = []
        summary.append(f"主体重量: {self.current_results.get('main_body_weight', 0):.2f} kg")
        summary.append(f"法兰重量: {self.current_results.get('inlet_flange_weight', 0) + self.current_results.get('outlet_flange_weight', 0):.2f} kg")
        summary.append(f"总重量: {self.current_results.get('total_weight', 0):.2f} kg")
        summary.append(f"总表面积: {self.current_results.get('total_surface_area', 0):.3f} m²")
        
        return " | ".join(summary)
