#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
参数输入面板模块
Parameter Input Panel Module
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QGroupBox, QComboBox, QDoubleSpinBox, QSpinBox,
                            QLabel, QPushButton, QCheckBox)
from PyQt5.QtCore import pyqtSignal, Qt
from PyQt5.QtGui import QFont

from ..core.config import AppConfig, ValidationConfig
from ..geometry.chute_geometry import ChuteParameters


class ParameterPanel(QWidget):
    """参数输入面板类"""

    # 信号定义
    parameters_changed = pyqtSignal(dict)

    def __init__(self):
        """初始化参数面板"""
        super().__init__()
        self.config = AppConfig()
        self.validation = ValidationConfig()
        self.init_ui()
        self.connect_signals()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)

        # 标题
        title_label = QLabel("参数设置")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)

        # 溜子类型选择
        self.create_chute_type_group(layout)

        # 基本参数
        self.create_basic_parameters_group(layout)

        # 进口参数
        self.create_inlet_parameters_group(layout)

        # 出口参数
        self.create_outlet_parameters_group(layout)

        # 材料和法兰参数
        self.create_material_flange_group(layout)

        # 控制按钮
        self.create_control_buttons(layout)

        # 添加弹性空间
        layout.addStretch()

    def create_chute_type_group(self, parent_layout):
        """创建溜子类型选择组"""
        group = QGroupBox("溜子类型")
        layout = QFormLayout(group)

        self.chute_type_combo = QComboBox()
        for key, value in self.config.CHUTE_TYPES.items():
            self.chute_type_combo.addItem(value, key)

        layout.addRow("类型:", self.chute_type_combo)
        parent_layout.addWidget(group)

    def create_basic_parameters_group(self, parent_layout):
        """创建基本参数组"""
        group = QGroupBox("基本参数")
        layout = QFormLayout(group)

        # 高度
        self.height_spin = QDoubleSpinBox()
        self.height_spin.setRange(self.validation.MIN_HEIGHT, self.validation.MAX_HEIGHT)
        self.height_spin.setValue(self.config.DEFAULT_CHUTE_HEIGHT)
        self.height_spin.setSuffix(" m")
        self.height_spin.setDecimals(3)
        layout.addRow("高度:", self.height_spin)

        # 板厚
        self.thickness_spin = QDoubleSpinBox()
        self.thickness_spin.setRange(self.validation.MIN_THICKNESS, self.validation.MAX_THICKNESS)
        self.thickness_spin.setValue(self.config.DEFAULT_PLATE_THICKNESS)
        self.thickness_spin.setSuffix(" m")
        self.thickness_spin.setDecimals(4)
        layout.addRow("板厚:", self.thickness_spin)

        # 偏移量
        self.offset_x_spin = QDoubleSpinBox()
        self.offset_x_spin.setRange(-self.validation.MAX_OFFSET, self.validation.MAX_OFFSET)
        self.offset_x_spin.setValue(0.0)
        self.offset_x_spin.setSuffix(" m")
        self.offset_x_spin.setDecimals(3)
        layout.addRow("X偏移:", self.offset_x_spin)

        self.offset_y_spin = QDoubleSpinBox()
        self.offset_y_spin.setRange(-self.validation.MAX_OFFSET, self.validation.MAX_OFFSET)
        self.offset_y_spin.setValue(0.0)
        self.offset_y_spin.setSuffix(" m")
        self.offset_y_spin.setDecimals(3)
        layout.addRow("Y偏移:", self.offset_y_spin)

        # 圆形分段数
        self.segments_spin = QSpinBox()
        self.segments_spin.setRange(8, 64)
        self.segments_spin.setValue(32)
        layout.addRow("圆形分段数:", self.segments_spin)

        parent_layout.addWidget(group)

    def create_inlet_parameters_group(self, parent_layout):
        """创建进口参数组"""
        group = QGroupBox("进口参数")
        layout = QFormLayout(group)

        # 进口形状
        self.inlet_shape_combo = QComboBox()
        self.inlet_shape_combo.addItem("方形", "square")
        self.inlet_shape_combo.addItem("圆形", "round")
        layout.addRow("形状:", self.inlet_shape_combo)

        # 方形尺寸
        self.inlet_width_spin = QDoubleSpinBox()
        self.inlet_width_spin.setRange(self.validation.MIN_DIMENSION, self.validation.MAX_DIMENSION)
        self.inlet_width_spin.setValue(1.0)
        self.inlet_width_spin.setSuffix(" m")
        self.inlet_width_spin.setDecimals(3)
        layout.addRow("宽度:", self.inlet_width_spin)

        self.inlet_length_spin = QDoubleSpinBox()
        self.inlet_length_spin.setRange(self.validation.MIN_DIMENSION, self.validation.MAX_DIMENSION)
        self.inlet_length_spin.setValue(1.0)
        self.inlet_length_spin.setSuffix(" m")
        self.inlet_length_spin.setDecimals(3)
        layout.addRow("长度:", self.inlet_length_spin)

        # 圆形尺寸
        self.inlet_diameter_spin = QDoubleSpinBox()
        self.inlet_diameter_spin.setRange(self.validation.MIN_DIMENSION, self.validation.MAX_DIMENSION)
        self.inlet_diameter_spin.setValue(1.0)
        self.inlet_diameter_spin.setSuffix(" m")
        self.inlet_diameter_spin.setDecimals(3)
        layout.addRow("直径:", self.inlet_diameter_spin)

        # 法兰类型
        self.inlet_flange_type_combo = QComboBox()
        for key, value in self.config.FLANGE_TYPES.items():
            self.inlet_flange_type_combo.addItem(value, key)
        layout.addRow("法兰类型:", self.inlet_flange_type_combo)

        # 法兰规格
        self.inlet_flange_spec_combo = QComboBox()
        self.update_flange_specs(self.inlet_flange_spec_combo, "angle")
        layout.addRow("法兰规格:", self.inlet_flange_spec_combo)

        parent_layout.addWidget(group)

    def create_outlet_parameters_group(self, parent_layout):
        """创建出口参数组"""
        group = QGroupBox("出口参数")
        layout = QFormLayout(group)

        # 出口形状
        self.outlet_shape_combo = QComboBox()
        self.outlet_shape_combo.addItem("方形", "square")
        self.outlet_shape_combo.addItem("圆形", "round")
        layout.addRow("形状:", self.outlet_shape_combo)

        # 方形尺寸
        self.outlet_width_spin = QDoubleSpinBox()
        self.outlet_width_spin.setRange(self.validation.MIN_DIMENSION, self.validation.MAX_DIMENSION)
        self.outlet_width_spin.setValue(0.5)
        self.outlet_width_spin.setSuffix(" m")
        self.outlet_width_spin.setDecimals(3)
        layout.addRow("宽度:", self.outlet_width_spin)

        self.outlet_length_spin = QDoubleSpinBox()
        self.outlet_length_spin.setRange(self.validation.MIN_DIMENSION, self.validation.MAX_DIMENSION)
        self.outlet_length_spin.setValue(0.5)
        self.outlet_length_spin.setSuffix(" m")
        self.outlet_length_spin.setDecimals(3)
        layout.addRow("长度:", self.outlet_length_spin)

        # 圆形尺寸
        self.outlet_diameter_spin = QDoubleSpinBox()
        self.outlet_diameter_spin.setRange(self.validation.MIN_DIMENSION, self.validation.MAX_DIMENSION)
        self.outlet_diameter_spin.setValue(0.5)
        self.outlet_diameter_spin.setSuffix(" m")
        self.outlet_diameter_spin.setDecimals(3)
        layout.addRow("直径:", self.outlet_diameter_spin)

        # 法兰类型
        self.outlet_flange_type_combo = QComboBox()
        for key, value in self.config.FLANGE_TYPES.items():
            self.outlet_flange_type_combo.addItem(value, key)
        layout.addRow("法兰类型:", self.outlet_flange_type_combo)

        # 法兰规格
        self.outlet_flange_spec_combo = QComboBox()
        self.update_flange_specs(self.outlet_flange_spec_combo, "angle")
        layout.addRow("法兰规格:", self.outlet_flange_spec_combo)

        parent_layout.addWidget(group)

    def create_material_flange_group(self, parent_layout):
        """创建材料参数组"""
        group = QGroupBox("材料参数")
        layout = QFormLayout(group)

        # 材料选择
        self.material_combo = QComboBox()
        for key, value in self.config.MATERIALS.items():
            self.material_combo.addItem(value["name"], key)
        layout.addRow("材料:", self.material_combo)

        parent_layout.addWidget(group)

    def create_control_buttons(self, parent_layout):
        """创建控制按钮"""
        button_layout = QHBoxLayout()

        self.update_button = QPushButton("更新模型")
        self.update_button.clicked.connect(self.emit_parameters_changed)
        button_layout.addWidget(self.update_button)

        self.reset_button = QPushButton("重置参数")
        self.reset_button.clicked.connect(self.reset_parameters)
        button_layout.addWidget(self.reset_button)

        parent_layout.addLayout(button_layout)

    def connect_signals(self):
        """连接信号和槽"""
        # 溜子类型改变时更新界面
        self.chute_type_combo.currentDataChanged.connect(self.on_chute_type_changed)

        # 形状改变时更新界面
        self.inlet_shape_combo.currentDataChanged.connect(self.on_inlet_shape_changed)
        self.outlet_shape_combo.currentDataChanged.connect(self.on_outlet_shape_changed)

        # 法兰类型改变时更新规格选项
        self.inlet_flange_type_combo.currentDataChanged.connect(
            lambda: self.on_flange_type_changed(self.inlet_flange_type_combo, self.inlet_flange_spec_combo)
        )
        self.outlet_flange_type_combo.currentDataChanged.connect(
            lambda: self.on_flange_type_changed(self.outlet_flange_type_combo, self.outlet_flange_spec_combo)
        )

        # 参数改变时自动更新（可选）
        # self.height_spin.valueChanged.connect(self.emit_parameters_changed)

    def on_chute_type_changed(self, chute_type):
        """溜子类型改变时的处理"""
        # 根据溜子类型设置默认的进出口形状
        if chute_type == "square_to_round":
            self.inlet_shape_combo.setCurrentData("square")
            self.outlet_shape_combo.setCurrentData("round")
        elif chute_type == "round_to_square":
            self.inlet_shape_combo.setCurrentData("round")
            self.outlet_shape_combo.setCurrentData("square")
        elif chute_type == "square_to_square":
            self.inlet_shape_combo.setCurrentData("square")
            self.outlet_shape_combo.setCurrentData("square")
        elif chute_type == "round_to_round":
            self.inlet_shape_combo.setCurrentData("round")
            self.outlet_shape_combo.setCurrentData("round")

        self.update_ui_visibility()

    def on_inlet_shape_changed(self, shape):
        """进口形状改变时的处理"""
        self.update_ui_visibility()

    def on_outlet_shape_changed(self, shape):
        """出口形状改变时的处理"""
        self.update_ui_visibility()

    def on_flange_type_changed(self, type_combo, spec_combo):
        """法兰类型改变时的处理"""
        flange_type = type_combo.currentData()
        self.update_flange_specs(spec_combo, flange_type)

    def update_flange_specs(self, spec_combo, flange_type):
        """更新法兰规格选项"""
        spec_combo.clear()

        if flange_type == "angle":
            for spec in self.config.ANGLE_STEEL_SPECS.keys():
                spec_combo.addItem(spec, spec)
        elif flange_type == "flat":
            for spec in self.config.FLAT_STEEL_SPECS.keys():
                spec_combo.addItem(spec, spec)

    def update_ui_visibility(self):
        """更新界面控件的可见性"""
        # 根据进口形状显示/隐藏相应控件
        inlet_shape = self.inlet_shape_combo.currentData()
        self.inlet_width_spin.setEnabled(inlet_shape == "square")
        self.inlet_length_spin.setEnabled(inlet_shape == "square")
        self.inlet_diameter_spin.setEnabled(inlet_shape == "round")

        # 根据出口形状显示/隐藏相应控件
        outlet_shape = self.outlet_shape_combo.currentData()
        self.outlet_width_spin.setEnabled(outlet_shape == "square")
        self.outlet_length_spin.setEnabled(outlet_shape == "square")
        self.outlet_diameter_spin.setEnabled(outlet_shape == "round")

    def get_parameters(self) -> ChuteParameters:
        """获取当前参数"""
        params = ChuteParameters(
            chute_type=self.chute_type_combo.currentData(),
            height=self.height_spin.value(),
            plate_thickness=self.thickness_spin.value(),
            material=self.material_combo.currentData(),

            # 进口参数
            inlet_shape=self.inlet_shape_combo.currentData(),
            inlet_width=self.inlet_width_spin.value() if self.inlet_shape_combo.currentData() == "square" else None,
            inlet_length=self.inlet_length_spin.value() if self.inlet_shape_combo.currentData() == "square" else None,
            inlet_diameter=self.inlet_diameter_spin.value() if self.inlet_shape_combo.currentData() == "round" else None,
            inlet_flange_type=self.inlet_flange_type_combo.currentData(),
            inlet_flange_spec=self.inlet_flange_spec_combo.currentData() or "",

            # 出口参数
            outlet_shape=self.outlet_shape_combo.currentData(),
            outlet_width=self.outlet_width_spin.value() if self.outlet_shape_combo.currentData() == "square" else None,
            outlet_length=self.outlet_length_spin.value() if self.outlet_shape_combo.currentData() == "square" else None,
            outlet_diameter=self.outlet_diameter_spin.value() if self.outlet_shape_combo.currentData() == "round" else None,
            outlet_flange_type=self.outlet_flange_type_combo.currentData(),
            outlet_flange_spec=self.outlet_flange_spec_combo.currentData() or "",

            # 偏移参数
            offset_x=self.offset_x_spin.value(),
            offset_y=self.offset_y_spin.value(),

            # 其他参数
            segments=self.segments_spin.value()
        )

        return params

    def emit_parameters_changed(self):
        """发射参数改变信号"""
        try:
            params = self.get_parameters()
            # 转换为字典格式
            params_dict = {
                'parameters': params,
                'chute_type': params.chute_type,
                'height': params.height,
                'plate_thickness': params.plate_thickness,
                'material': params.material
            }
            self.parameters_changed.emit(params_dict)
        except Exception as e:
            print(f"参数获取错误: {e}")

    def reset_parameters(self):
        """重置参数到默认值"""
        self.chute_type_combo.setCurrentIndex(0)
        self.height_spin.setValue(self.config.DEFAULT_CHUTE_HEIGHT)
        self.thickness_spin.setValue(self.config.DEFAULT_PLATE_THICKNESS)
        self.offset_x_spin.setValue(0.0)
        self.offset_y_spin.setValue(0.0)
        self.segments_spin.setValue(32)

        self.inlet_shape_combo.setCurrentIndex(0)
        self.inlet_width_spin.setValue(1.0)
        self.inlet_length_spin.setValue(1.0)
        self.inlet_diameter_spin.setValue(1.0)
        self.inlet_flange_type_combo.setCurrentIndex(0)

        self.outlet_shape_combo.setCurrentIndex(1)
        self.outlet_width_spin.setValue(0.5)
        self.outlet_length_spin.setValue(0.5)
        self.outlet_diameter_spin.setValue(0.5)
        self.outlet_flange_type_combo.setCurrentIndex(0)

        self.material_combo.setCurrentIndex(0)

        self.update_ui_visibility()