#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口模块
Main Window Module
"""

import sys
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QSplitter, QMenuBar, QStatusBar, QAction, QMessageBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon

from .parameter_panel import ParameterPanel
from .visualization_panel import VisualizationPanel
from .results_panel import ResultsPanel
from ..core.config import AppConfig


class MainWindow(QMainWindow):
    """主窗口类"""
    
    # 信号定义
    parameters_changed = pyqtSignal(dict)
    
    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        self.config = AppConfig()
        self.init_ui()
        self.connect_signals()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(f"{self.config.APP_NAME} v{self.config.APP_VERSION}")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 创建左侧面板（参数输入）
        self.parameter_panel = ParameterPanel()
        splitter.addWidget(self.parameter_panel)
        
        # 创建右侧分割器（3D视图和结果）
        right_splitter = QSplitter(Qt.Vertical)
        
        # 创建3D可视化面板
        self.visualization_panel = VisualizationPanel()
        right_splitter.addWidget(self.visualization_panel)
        
        # 创建结果显示面板
        self.results_panel = ResultsPanel()
        right_splitter.addWidget(self.results_panel)
        
        splitter.addWidget(right_splitter)
        
        # 设置分割器比例
        splitter.setSizes([350, 1050])  # 左侧350px，右侧1050px
        right_splitter.setSizes([600, 300])  # 上部600px，下部300px
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建状态栏
        self.create_status_bar()
        
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')
        
        new_action = QAction('新建(&N)', self)
        new_action.setShortcut('Ctrl+N')
        new_action.triggered.connect(self.new_project)
        file_menu.addAction(new_action)
        
        open_action = QAction('打开(&O)', self)
        open_action.setShortcut('Ctrl+O')
        open_action.triggered.connect(self.open_project)
        file_menu.addAction(open_action)
        
        save_action = QAction('保存(&S)', self)
        save_action.setShortcut('Ctrl+S')
        save_action.triggered.connect(self.save_project)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('退出(&X)', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 视图菜单
        view_menu = menubar.addMenu('视图(&V)')
        
        reset_view_action = QAction('重置视图(&R)', self)
        reset_view_action.triggered.connect(self.reset_view)
        view_menu.addAction(reset_view_action)
        
        wireframe_action = QAction('线框模式(&W)', self)
        wireframe_action.setCheckable(True)
        wireframe_action.triggered.connect(self.toggle_wireframe)
        view_menu.addAction(wireframe_action)
        
        transparent_action = QAction('半透明模式(&T)', self)
        transparent_action.setCheckable(True)
        transparent_action.triggered.connect(self.toggle_transparent)
        view_menu.addAction(transparent_action)
        
        # 计算菜单
        calc_menu = menubar.addMenu('计算(&C)')
        
        calculate_action = QAction('重新计算(&R)', self)
        calculate_action.setShortcut('F5')
        calculate_action.triggered.connect(self.recalculate)
        calc_menu.addAction(calculate_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')
        
        about_action = QAction('关于(&A)', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage('就绪')
        
    def connect_signals(self):
        """连接信号和槽"""
        # 连接参数面板的信号
        self.parameter_panel.parameters_changed.connect(self.on_parameters_changed)
        
        # 连接可视化面板的信号
        self.visualization_panel.status_message.connect(self.status_bar.showMessage)
        
    def on_parameters_changed(self, parameters):
        """参数改变时的处理"""
        try:
            # 更新3D视图
            self.visualization_panel.update_model(parameters)
            
            # 更新计算结果
            self.results_panel.update_results(parameters)
            
            self.status_bar.showMessage('模型已更新')
            
        except Exception as e:
            self.status_bar.showMessage(f'错误: {str(e)}')
            QMessageBox.warning(self, '警告', f'更新模型时发生错误:\n{str(e)}')
    
    def new_project(self):
        """新建项目"""
        self.parameter_panel.reset_parameters()
        self.visualization_panel.clear_model()
        self.results_panel.clear_results()
        self.status_bar.showMessage('新建项目')
    
    def open_project(self):
        """打开项目"""
        # TODO: 实现项目文件的打开功能
        self.status_bar.showMessage('打开项目功能待实现')
    
    def save_project(self):
        """保存项目"""
        # TODO: 实现项目文件的保存功能
        self.status_bar.showMessage('保存项目功能待实现')
    
    def reset_view(self):
        """重置视图"""
        self.visualization_panel.reset_view()
        self.status_bar.showMessage('视图已重置')
    
    def toggle_wireframe(self, checked):
        """切换线框模式"""
        self.visualization_panel.set_wireframe_mode(checked)
        mode = "线框" if checked else "实体"
        self.status_bar.showMessage(f'切换到{mode}模式')
    
    def toggle_transparent(self, checked):
        """切换半透明模式"""
        self.visualization_panel.set_transparent_mode(checked)
        mode = "半透明" if checked else "不透明"
        self.status_bar.showMessage(f'切换到{mode}模式')
    
    def recalculate(self):
        """重新计算"""
        parameters = self.parameter_panel.get_parameters()
        self.on_parameters_changed(parameters)
        self.status_bar.showMessage('重新计算完成')
    
    def show_about(self):
        """显示关于对话框"""
        about_text = f"""
        <h3>{self.config.APP_NAME}</h3>
        <p>版本: {self.config.APP_VERSION}</p>
        <p>作者: {self.config.APP_AUTHOR}</p>
        <p>这是一个用于溜子参数化设计和工程量计算的专业软件。</p>
        <p>支持多种溜子类型的三维建模和工程计算。</p>
        """
        QMessageBox.about(self, '关于', about_text)
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        reply = QMessageBox.question(self, '确认退出', 
                                   '确定要退出程序吗？',
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()
