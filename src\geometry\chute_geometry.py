#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
溜子几何计算模块
Chute Geometry Calculation Module
"""

import numpy as np
import math
from typing import Tuple, List, Dict, Optional
from dataclasses import dataclass


@dataclass
class ChuteParameters:
    """溜子参数数据类"""
    chute_type: str
    height: float
    plate_thickness: float
    material: str
    inlet_shape: str  # "square" or "round"
    outlet_shape: str  # "square" or "round"

    # 进口参数
    inlet_width: Optional[float] = None
    inlet_length: Optional[float] = None
    inlet_diameter: Optional[float] = None
    inlet_flange_type: str = "none"
    inlet_flange_spec: str = ""

    # 出口参数
    outlet_width: Optional[float] = None
    outlet_length: Optional[float] = None
    outlet_diameter: Optional[float] = None
    outlet_flange_type: str = "none"
    outlet_flange_spec: str = ""

    # 偏移参数
    offset_x: float = 0.0
    offset_y: float = 0.0

    # 其他参数
    corner_radius: float = 0.0  # 圆角半径
    segments: int = 32  # 圆形分段数


class ChuteGeometry:
    """溜子几何计算类"""

    def __init__(self, parameters: ChuteParameters):
        """初始化几何计算器"""
        self.params = parameters
        self.vertices = []
        self.faces = []
        self.normals = []

    def generate_geometry(self) -> Dict:
        """生成溜子几何体"""
        if self.params.chute_type == "square_to_round":
            return self._generate_square_to_round()
        elif self.params.chute_type == "round_to_square":
            return self._generate_round_to_square()
        elif self.params.chute_type == "square_to_square":
            return self._generate_square_to_square()
        elif self.params.chute_type == "round_to_round":
            return self._generate_round_to_round()
        else:
            raise ValueError(f"不支持的溜子类型: {self.params.chute_type}")

    def _generate_square_to_round(self) -> Dict:
        """生成方对圆过渡溜子"""
        vertices = []
        faces = []

        # 生成上口（方形）顶点
        inlet_points = self._generate_square_points(
            self.params.inlet_width,
            self.params.inlet_length,
            z=self.params.height
        )

        # 生成下口（圆形）顶点
        outlet_points = self._generate_circle_points(
            self.params.outlet_diameter,
            z=0,
            offset_x=self.params.offset_x,
            offset_y=self.params.offset_y
        )

        # 添加顶点
        vertices.extend(inlet_points)
        vertices.extend(outlet_points)

        # 生成侧面
        inlet_count = len(inlet_points)
        outlet_count = len(outlet_points)

        # 连接上下口生成侧面
        side_faces = self._connect_contours(inlet_count, outlet_count)
        faces.extend(side_faces)

        # 生成上下底面
        if inlet_count > 0:
            inlet_face = list(range(inlet_count))
            faces.append(inlet_face)

        if outlet_count > 0:
            outlet_face = list(range(inlet_count, inlet_count + outlet_count))
            outlet_face.reverse()  # 反向以确保法向量正确
            faces.append(outlet_face)

        return {
            "vertices": np.array(vertices),
            "faces": faces,
            "type": "square_to_round"
        }

    def _generate_round_to_square(self) -> Dict:
        """生成圆对方过渡溜子"""
        vertices = []
        faces = []

        # 生成上口（圆形）顶点
        inlet_points = self._generate_circle_points(
            self.params.inlet_diameter,
            z=self.params.height
        )

        # 生成下口（方形）顶点
        outlet_points = self._generate_square_points(
            self.params.outlet_width,
            self.params.outlet_length,
            z=0,
            offset_x=self.params.offset_x,
            offset_y=self.params.offset_y
        )

        # 添加顶点
        vertices.extend(inlet_points)
        vertices.extend(outlet_points)

        # 生成面片
        inlet_count = len(inlet_points)
        outlet_count = len(outlet_points)

        side_faces = self._connect_contours(inlet_count, outlet_count)
        faces.extend(side_faces)

        # 生成上下底面
        if inlet_count > 0:
            inlet_face = list(range(inlet_count))
            faces.append(inlet_face)

        if outlet_count > 0:
            outlet_face = list(range(inlet_count, inlet_count + outlet_count))
            outlet_face.reverse()
            faces.append(outlet_face)

        return {
            "vertices": np.array(vertices),
            "faces": faces,
            "type": "round_to_square"
        }

    def _generate_square_to_square(self) -> Dict:
        """生成方对方过渡溜子"""
        vertices = []
        faces = []

        # 生成上口（方形）顶点
        inlet_points = self._generate_square_points(
            self.params.inlet_width,
            self.params.inlet_length,
            z=self.params.height
        )

        # 生成下口（方形）顶点
        outlet_points = self._generate_square_points(
            self.params.outlet_width,
            self.params.outlet_length,
            z=0,
            offset_x=self.params.offset_x,
            offset_y=self.params.offset_y
        )

        # 添加顶点
        vertices.extend(inlet_points)
        vertices.extend(outlet_points)

        # 生成面片
        inlet_count = len(inlet_points)
        outlet_count = len(outlet_points)

        side_faces = self._connect_contours(inlet_count, outlet_count)
        faces.extend(side_faces)

        # 生成上下底面
        if inlet_count > 0:
            inlet_face = list(range(inlet_count))
            faces.append(inlet_face)

        if outlet_count > 0:
            outlet_face = list(range(inlet_count, inlet_count + outlet_count))
            outlet_face.reverse()
            faces.append(outlet_face)

        return {
            "vertices": np.array(vertices),
            "faces": faces,
            "type": "square_to_square"
        }

    def _generate_round_to_round(self) -> Dict:
        """生成圆对圆过渡溜子"""
        vertices = []
        faces = []

        # 生成上口（圆形）顶点
        inlet_points = self._generate_circle_points(
            self.params.inlet_diameter,
            z=self.params.height
        )

        # 生成下口（圆形）顶点
        outlet_points = self._generate_circle_points(
            self.params.outlet_diameter,
            z=0,
            offset_x=self.params.offset_x,
            offset_y=self.params.offset_y
        )

        # 添加顶点
        vertices.extend(inlet_points)
        vertices.extend(outlet_points)

        # 生成面片
        inlet_count = len(inlet_points)
        outlet_count = len(outlet_points)

        side_faces = self._connect_contours(inlet_count, outlet_count)
        faces.extend(side_faces)

        # 生成上下底面
        if inlet_count > 0:
            inlet_face = list(range(inlet_count))
            faces.append(inlet_face)

        if outlet_count > 0:
            outlet_face = list(range(inlet_count, inlet_count + outlet_count))
            outlet_face.reverse()
            faces.append(outlet_face)

        return {
            "vertices": np.array(vertices),
            "faces": faces,
            "type": "round_to_round"
        }

    def _generate_square_points(self, width: float, length: float, z: float = 0,
                               offset_x: float = 0, offset_y: float = 0) -> List[Tuple[float, float, float]]:
        """生成方形轮廓点"""
        half_width = width / 2
        half_length = length / 2

        points = [
            (-half_width + offset_x, -half_length + offset_y, z),
            (half_width + offset_x, -half_length + offset_y, z),
            (half_width + offset_x, half_length + offset_y, z),
            (-half_width + offset_x, half_length + offset_y, z)
        ]

        return points

    def _generate_circle_points(self, diameter: float, z: float = 0,
                               offset_x: float = 0, offset_y: float = 0) -> List[Tuple[float, float, float]]:
        """生成圆形轮廓点"""
        radius = diameter / 2
        points = []

        for i in range(self.params.segments):
            angle = 2 * math.pi * i / self.params.segments
            x = radius * math.cos(angle) + offset_x
            y = radius * math.sin(angle) + offset_y
            points.append((x, y, z))

        return points

    def _connect_contours(self, inlet_count: int, outlet_count: int) -> List[List[int]]:
        """连接上下轮廓生成侧面"""
        faces = []

        if inlet_count == outlet_count:
            # 相同点数，直接连接
            for i in range(inlet_count):
                next_i = (i + 1) % inlet_count

                # 创建四边形面片（分解为两个三角形）
                v1 = i
                v2 = next_i
                v3 = inlet_count + next_i
                v4 = inlet_count + i

                # 第一个三角形
                faces.append([v1, v2, v3])
                # 第二个三角形
                faces.append([v1, v3, v4])
        else:
            # 不同点数，需要插值连接
            # 这里使用简化的连接方法
            # 实际应用中可能需要更复杂的算法
            for i in range(min(inlet_count, outlet_count)):
                inlet_idx = i
                outlet_idx = int(i * outlet_count / inlet_count)
                next_inlet_idx = (i + 1) % inlet_count
                next_outlet_idx = int((i + 1) * outlet_count / inlet_count) % outlet_count

                v1 = inlet_idx
                v2 = next_inlet_idx
                v3 = inlet_count + next_outlet_idx
                v4 = inlet_count + outlet_idx

                faces.append([v1, v2, v3])
                faces.append([v1, v3, v4])

        return faces
