#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用程序配置模块
Application Configuration Module
"""

import os
from typing import Dict, Any


class AppConfig:
    """应用程序配置类"""
    
    # 应用程序信息
    APP_NAME = "溜子参数化设计软件"
    APP_VERSION = "1.0.0"
    APP_AUTHOR = "Engineering Tools"
    
    # 默认参数
    DEFAULT_MATERIAL_DENSITY = 7850  # kg/m³ (Q235钢)
    DEFAULT_PLATE_THICKNESS = 0.006  # 6mm
    DEFAULT_CHUTE_HEIGHT = 1.0  # 1m
    
    # 材料数据库
    MATERIALS = {
        "Q235": {"density": 7850, "name": "Q235碳素钢"},
        "Q345": {"density": 7850, "name": "Q345低合金钢"},
        "304": {"density": 7930, "name": "304不锈钢"},
        "316": {"density": 7980, "name": "316不锈钢"},
        "铝合金": {"density": 2700, "name": "铝合金"},
    }
    
    # 角钢规格数据 (规格: {截面积(cm²), 单位重量(kg/m)})
    ANGLE_STEEL_SPECS = {
        "L25×3": {"area": 1.459, "weight": 1.145},
        "L30×3": {"area": 1.786, "weight": 1.402},
        "L40×3": {"area": 2.366, "weight": 1.857},
        "L40×4": {"area": 3.086, "weight": 2.422},
        "L50×3": {"area": 2.976, "weight": 2.336},
        "L50×4": {"area": 3.896, "weight": 3.058},
        "L50×5": {"area": 4.806, "weight": 3.770},
        "L63×4": {"area": 4.896, "weight": 3.846},
        "L63×5": {"area": 6.056, "weight": 4.756},
        "L70×4": {"area": 5.496, "weight": 4.317},
        "L70×5": {"area": 6.806, "weight": 5.344},
        "L75×5": {"area": 7.356, "weight": 5.776},
        "L80×5": {"area": 7.856, "weight": 6.168},
        "L90×6": {"area": 10.616, "weight": 8.334},
        "L100×6": {"area": 11.816, "weight": 9.276},
        "L100×8": {"area": 15.416, "weight": 12.101},
    }
    
    # 扁钢规格数据 (宽×厚: {截面积(cm²), 单位重量(kg/m)})
    FLAT_STEEL_SPECS = {
        "20×3": {"area": 0.6, "weight": 0.471},
        "25×3": {"area": 0.75, "weight": 0.589},
        "30×3": {"area": 0.9, "weight": 0.706},
        "40×3": {"area": 1.2, "weight": 0.942},
        "40×4": {"area": 1.6, "weight": 1.256},
        "50×3": {"area": 1.5, "weight": 1.178},
        "50×4": {"area": 2.0, "weight": 1.570},
        "50×5": {"area": 2.5, "weight": 1.963},
        "60×4": {"area": 2.4, "weight": 1.884},
        "60×5": {"area": 3.0, "weight": 2.355},
        "60×6": {"area": 3.6, "weight": 2.826},
        "80×5": {"area": 4.0, "weight": 3.140},
        "80×6": {"area": 4.8, "weight": 3.768},
        "100×6": {"area": 6.0, "weight": 4.710},
        "100×8": {"area": 8.0, "weight": 6.280},
        "120×8": {"area": 9.6, "weight": 7.536},
    }
    
    # 溜子类型定义
    CHUTE_TYPES = {
        "square_to_round": "方对圆过渡",
        "round_to_square": "圆对方过渡", 
        "square_to_square": "方对方过渡",
        "round_to_round": "圆对圆过渡",
        "y_type": "Y型分流溜子",
        "elbow": "弯头溜子"
    }
    
    # 法兰类型
    FLANGE_TYPES = {
        "none": "无法兰",
        "angle": "角钢法兰",
        "flat": "扁钢法兰"
    }
    
    # 显示模式
    DISPLAY_MODES = {
        "solid": "实体显示",
        "wireframe": "线框显示",
        "transparent": "半透明显示",
        "solid_edges": "实体+边缘"
    }
    
    # 文件路径
    @staticmethod
    def get_data_dir():
        """获取数据目录路径"""
        return os.path.join(os.path.dirname(__file__), "..", "data")
    
    @staticmethod
    def get_assets_dir():
        """获取资源目录路径"""
        return os.path.join(os.path.dirname(__file__), "..", "..", "assets")


class ValidationConfig:
    """参数验证配置"""
    
    # 尺寸范围限制 (米)
    MIN_DIMENSION = 0.1  # 最小尺寸 10cm
    MAX_DIMENSION = 10.0  # 最大尺寸 10m
    
    # 板厚范围限制 (米)
    MIN_THICKNESS = 0.002  # 最小板厚 2mm
    MAX_THICKNESS = 0.050  # 最大板厚 50mm
    
    # 高度范围限制 (米)
    MIN_HEIGHT = 0.2  # 最小高度 20cm
    MAX_HEIGHT = 20.0  # 最大高度 20m
    
    # 偏移量范围限制 (米)
    MAX_OFFSET = 5.0  # 最大偏移量 5m
