#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三维可视化面板模块
3D Visualization Panel Module
"""

import numpy as np
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel
from PyQt5.QtCore import pyqtSignal, Qt

try:
    import pyvista as pv
    from pyvistaqt import QtInteractor
    PYVISTA_AVAILABLE = True
except ImportError:
    PYVISTA_AVAILABLE = False
    print("PyVista not available. 3D visualization will be disabled.")

from ..geometry.chute_geometry import ChuteGeometry, ChuteParameters
from ..calculation.engineering_calc import EngineeringCalculator


class VisualizationPanel(QWidget):
    """三维可视化面板类"""
    
    # 信号定义
    status_message = pyqtSignal(str)
    
    def __init__(self):
        """初始化可视化面板"""
        super().__init__()
        self.current_mesh = None
        self.current_parameters = None
        self.wireframe_mode = False
        self.transparent_mode = False
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        self.reset_view_btn = QPushButton("重置视图")
        self.reset_view_btn.clicked.connect(self.reset_view)
        toolbar_layout.addWidget(self.reset_view_btn)
        
        self.wireframe_btn = QPushButton("线框模式")
        self.wireframe_btn.setCheckable(True)
        self.wireframe_btn.clicked.connect(self.toggle_wireframe)
        toolbar_layout.addWidget(self.wireframe_btn)
        
        self.transparent_btn = QPushButton("半透明")
        self.transparent_btn.setCheckable(True)
        self.transparent_btn.clicked.connect(self.toggle_transparent)
        toolbar_layout.addWidget(self.transparent_btn)
        
        toolbar_layout.addStretch()
        
        self.info_label = QLabel("就绪")
        toolbar_layout.addWidget(self.info_label)
        
        layout.addLayout(toolbar_layout)
        
        # 3D视图区域
        if PYVISTA_AVAILABLE:
            self.plotter = QtInteractor(self)
            layout.addWidget(self.plotter.interactor)
            
            # 设置背景和基本属性
            self.plotter.set_background('white')
            self.plotter.show_axes()
            self.plotter.show_grid()
            
        else:
            # 如果PyVista不可用，显示占位符
            placeholder = QLabel("3D可视化功能需要安装PyVista库")
            placeholder.setAlignment(Qt.AlignCenter)
            placeholder.setStyleSheet("border: 1px solid gray; background-color: #f0f0f0;")
            layout.addWidget(placeholder)
            
    def update_model(self, parameters_dict):
        """更新3D模型"""
        if not PYVISTA_AVAILABLE:
            self.status_message.emit("PyVista不可用，无法显示3D模型")
            return
            
        try:
            parameters = parameters_dict.get('parameters')
            if not parameters:
                return
                
            self.current_parameters = parameters
            
            # 生成几何体
            geometry_generator = ChuteGeometry(parameters)
            geometry_data = geometry_generator.generate_geometry()
            
            # 清除之前的模型
            self.plotter.clear()
            
            # 创建网格
            mesh = self._create_mesh_from_geometry(geometry_data)
            
            if mesh:
                self.current_mesh = mesh
                
                # 添加到场景
                actor = self.plotter.add_mesh(
                    mesh,
                    color='lightblue',
                    show_edges=True,
                    opacity=0.7 if self.transparent_mode else 1.0,
                    style='wireframe' if self.wireframe_mode else 'surface'
                )
                
                # 重置视图
                self.plotter.reset_camera()
                
                self.info_label.setText(f"顶点: {mesh.n_points}, 面片: {mesh.n_cells}")
                self.status_message.emit("模型更新成功")
            else:
                self.status_message.emit("模型生成失败")
                
        except Exception as e:
            self.status_message.emit(f"模型更新错误: {str(e)}")
            print(f"可视化错误: {e}")
            
    def _create_mesh_from_geometry(self, geometry_data):
        """从几何数据创建PyVista网格"""
        if not PYVISTA_AVAILABLE:
            return None
            
        try:
            vertices = geometry_data["vertices"]
            faces = geometry_data["faces"]
            
            if len(vertices) == 0 or len(faces) == 0:
                return None
            
            # 转换面片格式为PyVista格式
            pv_faces = []
            for face in faces:
                pv_faces.append(len(face))  # 面片顶点数
                pv_faces.extend(face)       # 顶点索引
            
            # 创建PolyData
            mesh = pv.PolyData(vertices, pv_faces)
            
            # 计算法向量
            mesh = mesh.compute_normals()
            
            return mesh
            
        except Exception as e:
            print(f"网格创建错误: {e}")
            return None
            
    def clear_model(self):
        """清除模型"""
        if PYVISTA_AVAILABLE and hasattr(self, 'plotter'):
            self.plotter.clear()
            self.current_mesh = None
            self.current_parameters = None
            self.info_label.setText("就绪")
            
    def reset_view(self):
        """重置视图"""
        if PYVISTA_AVAILABLE and hasattr(self, 'plotter'):
            self.plotter.reset_camera()
            self.status_message.emit("视图已重置")
            
    def toggle_wireframe(self, checked):
        """切换线框模式"""
        self.wireframe_mode = checked
        self.set_wireframe_mode(checked)
        
    def set_wireframe_mode(self, enabled):
        """设置线框模式"""
        if not PYVISTA_AVAILABLE or not self.current_mesh:
            return
            
        try:
            self.plotter.clear()
            
            style = 'wireframe' if enabled else 'surface'
            self.plotter.add_mesh(
                self.current_mesh,
                color='lightblue',
                show_edges=True,
                opacity=0.7 if self.transparent_mode else 1.0,
                style=style
            )
            
            self.wireframe_btn.setChecked(enabled)
            
        except Exception as e:
            self.status_message.emit(f"切换显示模式错误: {str(e)}")
            
    def toggle_transparent(self, checked):
        """切换半透明模式"""
        self.transparent_mode = checked
        self.set_transparent_mode(checked)
        
    def set_transparent_mode(self, enabled):
        """设置半透明模式"""
        if not PYVISTA_AVAILABLE or not self.current_mesh:
            return
            
        try:
            self.plotter.clear()
            
            opacity = 0.7 if enabled else 1.0
            self.plotter.add_mesh(
                self.current_mesh,
                color='lightblue',
                show_edges=True,
                opacity=opacity,
                style='wireframe' if self.wireframe_mode else 'surface'
            )
            
            self.transparent_btn.setChecked(enabled)
            
        except Exception as e:
            self.status_message.emit(f"切换透明度错误: {str(e)}")
            
    def export_model(self, filename):
        """导出模型"""
        if not PYVISTA_AVAILABLE or not self.current_mesh:
            return False
            
        try:
            self.current_mesh.save(filename)
            self.status_message.emit(f"模型已导出到: {filename}")
            return True
        except Exception as e:
            self.status_message.emit(f"导出失败: {str(e)}")
            return False
            
    def take_screenshot(self, filename):
        """截图"""
        if not PYVISTA_AVAILABLE:
            return False
            
        try:
            self.plotter.screenshot(filename)
            self.status_message.emit(f"截图已保存到: {filename}")
            return True
        except Exception as e:
            self.status_message.emit(f"截图失败: {str(e)}")
            return False
