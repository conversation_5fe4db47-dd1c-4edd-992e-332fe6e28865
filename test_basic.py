#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基本功能测试脚本
Basic Functionality Test Script
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.geometry.chute_geometry import ChuteParameters, ChuteGeometry
from src.calculation.engineering_calc import EngineeringCalculator
from src.core.config import AppConfig


def test_geometry_generation():
    """测试几何生成功能"""
    print("=== 测试几何生成功能 ===")
    
    # 创建测试参数
    params = ChuteParameters(
        chute_type="square_to_round",
        height=1.0,
        plate_thickness=0.006,
        material="Q235",
        inlet_shape="square",
        inlet_width=1.0,
        inlet_length=1.0,
        outlet_shape="round",
        outlet_diameter=0.5,
        inlet_flange_type="angle",
        inlet_flange_spec="L50×5",
        outlet_flange_type="angle",
        outlet_flange_spec="L50×5"
    )
    
    try:
        # 生成几何体
        geometry_generator = ChuteGeometry(params)
        geometry_data = geometry_generator.generate_geometry()
        
        print(f"✓ 几何生成成功")
        print(f"  顶点数量: {len(geometry_data['vertices'])}")
        print(f"  面片数量: {len(geometry_data['faces'])}")
        print(f"  类型: {geometry_data['type']}")
        
        return geometry_data, params
        
    except Exception as e:
        print(f"✗ 几何生成失败: {e}")
        return None, None


def test_engineering_calculation(geometry_data, params):
    """测试工程计算功能"""
    print("\n=== 测试工程计算功能 ===")
    
    if not geometry_data or not params:
        print("✗ 无法进行计算测试，几何数据无效")
        return
    
    try:
        # 创建计算器
        calculator = EngineeringCalculator(params, geometry_data)
        
        # 计算所有工程量
        results = calculator.calculate_all()
        
        print("✓ 工程计算成功")
        print("计算结果:")
        print(f"  总表面积: {results.get('total_surface_area', 0):.3f} m²")
        print(f"  主体重量: {results.get('main_body_weight', 0):.2f} kg")
        print(f"  进口法兰长度: {results.get('inlet_flange_length', 0):.3f} m")
        print(f"  进口法兰重量: {results.get('inlet_flange_weight', 0):.2f} kg")
        print(f"  出口法兰长度: {results.get('outlet_flange_length', 0):.3f} m")
        print(f"  出口法兰重量: {results.get('outlet_flange_weight', 0):.2f} kg")
        print(f"  总重量: {results.get('total_weight', 0):.2f} kg")
        
    except Exception as e:
        print(f"✗ 工程计算失败: {e}")


def test_config_loading():
    """测试配置加载功能"""
    print("\n=== 测试配置加载功能 ===")
    
    try:
        config = AppConfig()
        
        print("✓ 配置加载成功")
        print(f"  应用名称: {config.APP_NAME}")
        print(f"  应用版本: {config.APP_VERSION}")
        print(f"  材料数量: {len(config.MATERIALS)}")
        print(f"  角钢规格数量: {len(config.ANGLE_STEEL_SPECS)}")
        print(f"  扁钢规格数量: {len(config.FLAT_STEEL_SPECS)}")
        print(f"  溜子类型数量: {len(config.CHUTE_TYPES)}")
        
    except Exception as e:
        print(f"✗ 配置加载失败: {e}")


def test_different_chute_types():
    """测试不同溜子类型"""
    print("\n=== 测试不同溜子类型 ===")
    
    chute_types = [
        "square_to_round",
        "round_to_square", 
        "square_to_square",
        "round_to_round"
    ]
    
    for chute_type in chute_types:
        try:
            # 创建参数
            params = ChuteParameters(
                chute_type=chute_type,
                height=1.0,
                plate_thickness=0.006,
                material="Q235",
                inlet_shape="square" if "square" in chute_type.split("_")[0] else "round",
                inlet_width=1.0 if "square" in chute_type.split("_")[0] else None,
                inlet_length=1.0 if "square" in chute_type.split("_")[0] else None,
                inlet_diameter=1.0 if "round" in chute_type.split("_")[0] else None,
                outlet_shape="square" if "square" in chute_type.split("_")[2] else "round",
                outlet_width=0.5 if "square" in chute_type.split("_")[2] else None,
                outlet_length=0.5 if "square" in chute_type.split("_")[2] else None,
                outlet_diameter=0.5 if "round" in chute_type.split("_")[2] else None,
            )
            
            # 生成几何体
            geometry_generator = ChuteGeometry(params)
            geometry_data = geometry_generator.generate_geometry()
            
            print(f"✓ {chute_type}: 顶点{len(geometry_data['vertices'])}, 面片{len(geometry_data['faces'])}")
            
        except Exception as e:
            print(f"✗ {chute_type}: {e}")


def main():
    """主测试函数"""
    print("溜子参数化设计软件 - 基本功能测试")
    print("=" * 50)
    
    # 测试配置加载
    test_config_loading()
    
    # 测试几何生成
    geometry_data, params = test_geometry_generation()
    
    # 测试工程计算
    test_engineering_calculation(geometry_data, params)
    
    # 测试不同溜子类型
    test_different_chute_types()
    
    print("\n" + "=" * 50)
    print("测试完成！")


if __name__ == "__main__":
    main()
