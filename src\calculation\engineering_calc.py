#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工程计算模块
Engineering Calculation Module
"""

import numpy as np
import math
from typing import Dict, List, Tuple
from ..geometry.chute_geometry import ChuteParameters
from ..core.config import AppConfig


class EngineeringCalculator:
    """工程计算器类"""
    
    def __init__(self, parameters: ChuteParameters, geometry_data: Dict):
        """初始化计算器"""
        self.params = parameters
        self.geometry = geometry_data
        self.config = AppConfig()
    
    def calculate_all(self) -> Dict:
        """计算所有工程量"""
        results = {}
        
        # 计算主体相关数据
        results.update(self.calculate_main_body())
        
        # 计算法兰相关数据
        results.update(self.calculate_flanges())
        
        # 计算总重量
        results["total_weight"] = (
            results.get("main_body_weight", 0) + 
            results.get("inlet_flange_weight", 0) + 
            results.get("outlet_flange_weight", 0)
        )
        
        return results
    
    def calculate_main_body(self) -> Dict:
        """计算主体工程量"""
        results = {}
        
        # 计算表面积
        surface_areas = self.calculate_surface_area()
        results.update(surface_areas)
        
        # 计算体积
        volume = self.calculate_volume()
        results["main_body_volume"] = volume
        
        # 计算重量
        material_density = self.config.MATERIALS[self.params.material]["density"]
        weight = volume * material_density
        results["main_body_weight"] = weight
        
        return results
    
    def calculate_surface_area(self) -> Dict:
        """计算表面积"""
        vertices = self.geometry["vertices"]
        faces = self.geometry["faces"]
        
        total_area = 0.0
        side_area = 0.0
        inlet_area = 0.0
        outlet_area = 0.0
        
        for i, face in enumerate(faces):
            area = self._calculate_face_area(vertices, face)
            total_area += area
            
            # 区分不同类型的面
            if i == len(faces) - 2:  # 倒数第二个面通常是进口
                inlet_area = area
            elif i == len(faces) - 1:  # 最后一个面通常是出口
                outlet_area = area
            else:  # 其他面是侧面
                side_area += area
        
        return {
            "total_surface_area": total_area,
            "side_surface_area": side_area,
            "inlet_area": inlet_area,
            "outlet_area": outlet_area
        }
    
    def calculate_volume(self) -> float:
        """计算主体体积（基于板厚的近似计算）"""
        surface_area = self.calculate_surface_area()["total_surface_area"]
        volume = surface_area * self.params.plate_thickness
        return volume
    
    def calculate_flanges(self) -> Dict:
        """计算法兰工程量"""
        results = {}
        
        # 计算进口法兰
        if self.params.inlet_flange_type != "none":
            inlet_flange = self._calculate_single_flange(
                shape=self.params.inlet_shape,
                width=self.params.inlet_width,
                length=self.params.inlet_length,
                diameter=self.params.inlet_diameter,
                flange_type=self.params.inlet_flange_type,
                flange_spec=self.params.inlet_flange_spec
            )
            results["inlet_flange_length"] = inlet_flange["length"]
            results["inlet_flange_weight"] = inlet_flange["weight"]
        else:
            results["inlet_flange_length"] = 0.0
            results["inlet_flange_weight"] = 0.0
        
        # 计算出口法兰
        if self.params.outlet_flange_type != "none":
            outlet_flange = self._calculate_single_flange(
                shape=self.params.outlet_shape,
                width=self.params.outlet_width,
                length=self.params.outlet_length,
                diameter=self.params.outlet_diameter,
                flange_type=self.params.outlet_flange_type,
                flange_spec=self.params.outlet_flange_spec
            )
            results["outlet_flange_length"] = outlet_flange["length"]
            results["outlet_flange_weight"] = outlet_flange["weight"]
        else:
            results["outlet_flange_length"] = 0.0
            results["outlet_flange_weight"] = 0.0
        
        return results
    
    def _calculate_single_flange(self, shape: str, width: float = None, 
                                length: float = None, diameter: float = None,
                                flange_type: str = "angle", flange_spec: str = "") -> Dict:
        """计算单个法兰的工程量"""
        # 计算周长
        if shape == "square":
            perimeter = 2 * (width + length)
        elif shape == "round":
            perimeter = math.pi * diameter
        else:
            raise ValueError(f"不支持的形状: {shape}")
        
        # 获取法兰材料的单位重量
        unit_weight = self._get_flange_unit_weight(flange_type, flange_spec)
        
        # 计算总重量
        total_weight = perimeter * unit_weight
        
        return {
            "length": perimeter,
            "weight": total_weight
        }
    
    def _get_flange_unit_weight(self, flange_type: str, flange_spec: str) -> float:
        """获取法兰材料的单位重量 (kg/m)"""
        if flange_type == "angle":
            if flange_spec in self.config.ANGLE_STEEL_SPECS:
                return self.config.ANGLE_STEEL_SPECS[flange_spec]["weight"]
            else:
                # 默认值
                return 3.0
        elif flange_type == "flat":
            if flange_spec in self.config.FLAT_STEEL_SPECS:
                return self.config.FLAT_STEEL_SPECS[flange_spec]["weight"]
            else:
                # 默认值
                return 2.0
        else:
            return 0.0
    
    def _calculate_face_area(self, vertices: np.ndarray, face: List[int]) -> float:
        """计算面片面积"""
        if len(face) < 3:
            return 0.0
        
        # 对于三角形面片
        if len(face) == 3:
            v1, v2, v3 = vertices[face[0]], vertices[face[1]], vertices[face[2]]
            # 使用叉乘计算三角形面积
            edge1 = v2 - v1
            edge2 = v3 - v1
            cross_product = np.cross(edge1, edge2)
            area = 0.5 * np.linalg.norm(cross_product)
            return area
        
        # 对于多边形面片，分解为三角形
        total_area = 0.0
        for i in range(1, len(face) - 1):
            triangle = [face[0], face[i], face[i + 1]]
            area = self._calculate_face_area(vertices, triangle)
            total_area += area
        
        return total_area
    
    def calculate_expanded_area(self) -> Dict:
        """计算展开面积（用于下料参考）"""
        # 这是一个复杂的计算，需要根据具体的几何形状来实现
        # 这里提供一个简化的实现
        
        results = {}
        
        if self.params.chute_type in ["square_to_round", "round_to_square"]:
            # 对于方圆过渡，侧面是不可展曲面，需要分段近似
            side_area = self.calculate_surface_area()["side_surface_area"]
            results["expanded_side_area"] = side_area * 1.1  # 考虑展开损耗
        else:
            # 对于方方或圆圆过渡，可以精确计算展开面积
            side_area = self.calculate_surface_area()["side_surface_area"]
            results["expanded_side_area"] = side_area
        
        # 进出口面积
        surface_areas = self.calculate_surface_area()
        results["inlet_expanded_area"] = surface_areas["inlet_area"]
        results["outlet_expanded_area"] = surface_areas["outlet_area"]
        
        results["total_expanded_area"] = (
            results["expanded_side_area"] + 
            results["inlet_expanded_area"] + 
            results["outlet_expanded_area"]
        )
        
        return results
