#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装脚本
Installation Script for Chute Design Software
"""

import subprocess
import sys
import os


def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("需要Python 3.7或更高版本")
        return False
    else:
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True


def install_package(package):
    """安装单个包"""
    try:
        print(f"正在安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ {package} 安装失败")
        return False


def install_requirements():
    """安装依赖包"""
    print("\n开始安装依赖包...")
    
    # 基础包列表
    packages = [
        "PyQt5>=5.15.0",
        "numpy>=1.21.0",
        "scipy>=1.7.0",
        "pandas>=1.3.0",
        "shapely>=1.8.0"
    ]
    
    # 可选包（可能安装失败）
    optional_packages = [
        "pyvista>=0.42.0",
        "vtk>=9.2.0"
    ]
    
    success_count = 0
    total_count = len(packages)
    
    # 安装基础包
    for package in packages:
        if install_package(package):
            success_count += 1
    
    # 尝试安装可选包
    print("\n安装可选包（3D可视化功能）...")
    optional_success = 0
    for package in optional_packages:
        if install_package(package):
            optional_success += 1
    
    print(f"\n安装结果:")
    print(f"基础包: {success_count}/{total_count} 成功")
    print(f"可选包: {optional_success}/{len(optional_packages)} 成功")
    
    if success_count == total_count:
        print("✅ 基础功能安装完成")
        if optional_success == len(optional_packages):
            print("✅ 3D可视化功能可用")
        else:
            print("⚠️  3D可视化功能可能不可用")
        return True
    else:
        print("❌ 安装未完成，请检查错误信息")
        return False


def create_desktop_shortcut():
    """创建桌面快捷方式（Windows）"""
    if sys.platform != "win32":
        return
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "溜子设计软件.lnk")
        target = os.path.join(os.getcwd(), "main.py")
        wDir = os.getcwd()
        icon = target
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{target}"'
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = icon
        shortcut.save()
        
        print("✅ 桌面快捷方式创建成功")
    except ImportError:
        print("⚠️  无法创建桌面快捷方式（需要pywin32）")
    except Exception as e:
        print(f"⚠️  创建桌面快捷方式失败: {e}")


def run_basic_test():
    """运行基本测试"""
    print("\n运行基本功能测试...")
    try:
        result = subprocess.run([sys.executable, "test_basic.py"], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("✅ 基本功能测试通过")
            return True
        else:
            print("❌ 基本功能测试失败")
            print("错误信息:")
            print(result.stderr)
            return False
    except subprocess.TimeoutExpired:
        print("❌ 测试超时")
        return False
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")
        return False


def main():
    """主安装函数"""
    print("=" * 60)
    print("溜子参数化设计与分析软件 - 安装程序")
    print("Chute Parametric Design Software - Installer")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        input("按回车键退出...")
        return
    
    # 升级pip
    print("\n升级pip...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        print("✅ pip升级成功")
    except:
        print("⚠️  pip升级失败，继续安装...")
    
    # 安装依赖
    if not install_requirements():
        print("\n安装失败，请检查网络连接和权限设置")
        input("按回车键退出...")
        return
    
    # 运行测试
    if run_basic_test():
        print("\n🎉 安装成功！")
        print("\n使用方法:")
        print("1. 运行 'python main.py' 启动程序")
        print("2. 或者双击桌面快捷方式（如果已创建）")
        
        # 询问是否创建快捷方式
        if sys.platform == "win32":
            response = input("\n是否创建桌面快捷方式？(y/n): ")
            if response.lower() in ['y', 'yes', '是']:
                create_desktop_shortcut()
        
        # 询问是否立即运行
        response = input("\n是否立即运行程序？(y/n): ")
        if response.lower() in ['y', 'yes', '是']:
            print("正在启动程序...")
            subprocess.Popen([sys.executable, "main.py"])
    else:
        print("\n安装完成但测试失败，请检查配置")
    
    input("\n按回车键退出...")


if __name__ == "__main__":
    main()
