#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的GUI测试程序
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel, QPushButton
from PyQt5.QtCore import Qt


class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("溜子设计软件 - 测试窗口")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加标签
        title_label = QLabel("溜子参数化设计软件")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; margin: 20px;")
        layout.addWidget(title_label)
        
        status_label = QLabel("GUI测试成功！软件界面正常工作。")
        status_label.setAlignment(Qt.AlignCenter)
        status_label.setStyleSheet("font-size: 16px; color: green; margin: 20px;")
        layout.addWidget(status_label)
        
        info_label = QLabel("""
        如果您能看到这个窗口，说明：
        ✅ Python环境正常
        ✅ PyQt5安装成功
        ✅ GUI界面可以正常显示
        
        接下来可以运行完整版本的软件。
        """)
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("font-size: 14px; margin: 20px;")
        layout.addWidget(info_label)
        
        # 添加按钮
        close_button = QPushButton("关闭测试窗口")
        close_button.clicked.connect(self.close)
        close_button.setStyleSheet("font-size: 14px; padding: 10px; margin: 20px;")
        layout.addWidget(close_button)


def main():
    print("正在启动GUI测试...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("溜子设计软件测试")
    
    # 创建并显示窗口
    window = TestWindow()
    window.show()
    
    print("测试窗口已启动，请查看是否有窗口显示...")
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
