# 溜子参数化设计与分析软件

## 项目简介

这是一个基于Python开发的溜子（Chute/Hopper）参数化设计与分析软件。该软件允许用户通过输入几何参数实时生成溜子的三维模型，并能清晰展示其内部构造。此外，软件还提供溜子主体钢材的表面积、重量计算，以及进出口法兰（角钢或扁钢）的长度和重量计算功能。

## 主要功能

### 🏗️ 参数化建模
- 支持多种溜子类型：方对圆、圆对方、方对方、圆对圆过渡
- 用户可通过输入关键尺寸参数定义不同类型的溜子
- 支持偏心设计和自定义板厚

### 🎨 实时三维可视化
- 输入参数后即时更新并显示溜子的三维形态
- 支持旋转、缩放、平移等交互操作
- 多种显示模式：实体、线框、半透明
- 内部结构展示功能

### 📊 工程量计算
- 精确计算溜子主体所用钢板的表面积和重量
- 根据用户选择的法兰类型（角钢/扁钢）及其规格，计算进出口法兰所需材料的长度和重量
- 支持多种常用钢材材质选择

### 🔧 用户友好界面
- 直观、易于操作的用户界面
- 参数分组管理，便于输入和修改
- 实时结果显示和详细信息查看

## 系统要求

- Python 3.7+
- Windows 10/11 (推荐)
- 内存: 4GB以上
- 显卡: 支持OpenGL 3.0+

## 安装说明

### 1. 克隆项目
```bash
git clone <repository-url>
cd chute-design-software
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 运行程序
```bash
python main.py
```

## 依赖库说明

- **PyQt5**: GUI框架，提供用户界面
- **PyVista**: 三维可视化库，基于VTK
- **NumPy**: 数值计算库
- **SciPy**: 科学计算库

## 使用指南

### 基本操作流程

1. **选择溜子类型**: 在左侧参数面板选择所需的溜子类型
2. **输入几何参数**: 根据选择的类型输入相应的尺寸参数
3. **设置材料和法兰**: 选择钢材材质和法兰类型及规格
4. **生成模型**: 点击"更新模型"按钮生成三维模型
5. **查看结果**: 在右下角查看详细的工程量计算结果

### 参数说明

#### 基本参数
- **高度**: 溜子的总高度 (m)
- **板厚**: 钢板厚度 (m)
- **X偏移/Y偏移**: 上下口中心线的水平偏移量 (m)

#### 进出口参数
- **形状**: 方形或圆形
- **尺寸**: 根据形状输入宽度、长度或直径
- **法兰类型**: 无法兰、角钢法兰或扁钢法兰
- **法兰规格**: 选择具体的角钢或扁钢规格

#### 材料参数
- **材料**: 选择钢材类型（Q235、Q345、304不锈钢等）

### 三维视图操作

- **旋转**: 鼠标左键拖动
- **缩放**: 鼠标滚轮
- **平移**: 鼠标中键拖动
- **重置视图**: 点击"重置视图"按钮
- **显示模式**: 切换线框模式和半透明模式

## 支持的溜子类型

1. **方对圆过渡**: 上口为矩形，下口为圆形
2. **圆对方过渡**: 上口为圆形，下口为矩形
3. **方对方过渡**: 上口为矩形，下口为矩形（可等径或变径，可同心或偏心）
4. **圆对圆过渡**: 上口为圆形，下口为圆形（可等径或变径，可同心或偏心）

## 计算功能

### 主体工程量
- 总表面积、侧面积、进出口面积
- 主体体积和重量计算
- 基于选定材质密度的精确计算

### 法兰工程量
- 进出口法兰周长计算
- 角钢/扁钢材料长度和重量计算
- 支持常用规格的角钢和扁钢

## 项目结构

```
chute-design-software/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖库列表
├── README.md              # 项目说明文档
├── src/                   # 源代码目录
│   ├── core/              # 核心模块
│   │   └── config.py      # 配置文件
│   ├── geometry/          # 几何计算模块
│   │   └── chute_geometry.py
│   ├── calculation/       # 工程计算模块
│   │   └── engineering_calc.py
│   ├── gui/               # 用户界面模块
│   │   ├── main_window.py
│   │   ├── parameter_panel.py
│   │   ├── visualization_panel.py
│   │   └── results_panel.py
│   ├── data/              # 数据文件
│   └── utils/             # 工具函数
├── tests/                 # 测试文件
├── docs/                  # 文档目录
└── assets/                # 资源文件
```

## 开发说明

### 扩展新的溜子类型
1. 在 `config.py` 中添加新类型定义
2. 在 `chute_geometry.py` 中实现几何生成算法
3. 更新用户界面以支持新参数

### 添加新的材料或规格
1. 在 `config.py` 中的相应字典中添加新数据
2. 确保包含密度和单位重量信息

## 故障排除

### 常见问题

1. **PyVista安装失败**
   - 确保已安装Visual C++ Redistributable
   - 尝试使用conda安装: `conda install pyvista`

2. **3D显示异常**
   - 检查显卡驱动是否支持OpenGL 3.0+
   - 尝试更新显卡驱动

3. **计算结果异常**
   - 检查输入参数是否在合理范围内
   - 确保几何参数的一致性

## 版本历史

- **v1.0.0** (2025-01): 初始版本，支持基本的溜子类型和工程量计算

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 联系方式

如有问题或建议，请联系开发团队。

---

**注意**: 本软件仅供工程设计参考使用，实际工程应用请结合具体规范和标准进行验证。
