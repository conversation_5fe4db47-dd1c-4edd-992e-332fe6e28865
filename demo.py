#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
溜子设计软件演示脚本
Demo Script for Chute Design Software
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.geometry.chute_geometry import ChuteParameters, ChuteGeometry
from src.calculation.engineering_calc import EngineeringCalculator
from src.core.config import AppConfig


def demo_square_to_round():
    """演示方对圆溜子设计"""
    print("=" * 60)
    print("演示案例1: 方对圆过渡溜子")
    print("=" * 60)
    
    # 创建参数
    params = ChuteParameters(
        chute_type="square_to_round",
        height=1.5,  # 1.5米高
        plate_thickness=0.008,  # 8mm板厚
        material="Q235",
        inlet_shape="square",
        inlet_width=1.2,  # 进口1.2m×1.0m
        inlet_length=1.0,
        outlet_shape="round",
        outlet_diameter=0.6,  # 出口直径0.6m
        inlet_flange_type="angle",
        inlet_flange_spec="L50×5",
        outlet_flange_type="angle", 
        outlet_flange_spec="L40×4",
        offset_x=0.1,  # X方向偏移0.1m
        offset_y=0.0
    )
    
    print(f"设计参数:")
    print(f"  溜子类型: 方对圆过渡")
    print(f"  高度: {params.height} m")
    print(f"  板厚: {params.plate_thickness*1000} mm")
    print(f"  进口: {params.inlet_width}×{params.inlet_length} m (方形)")
    print(f"  出口: φ{params.outlet_diameter} m (圆形)")
    print(f"  偏移: X={params.offset_x} m, Y={params.offset_y} m")
    print(f"  材料: {params.material}")
    
    # 生成几何体和计算结果
    geometry_generator = ChuteGeometry(params)
    geometry_data = geometry_generator.generate_geometry()
    
    calculator = EngineeringCalculator(params, geometry_data)
    results = calculator.calculate_all()
    
    print(f"\n几何信息:")
    print(f"  顶点数量: {len(geometry_data['vertices'])}")
    print(f"  面片数量: {len(geometry_data['faces'])}")
    
    print(f"\n工程量计算结果:")
    print(f"  总表面积: {results.get('total_surface_area', 0):.3f} m²")
    print(f"  主体重量: {results.get('main_body_weight', 0):.2f} kg")
    print(f"  进口法兰: {results.get('inlet_flange_length', 0):.3f} m, {results.get('inlet_flange_weight', 0):.2f} kg")
    print(f"  出口法兰: {results.get('outlet_flange_length', 0):.3f} m, {results.get('outlet_flange_weight', 0):.2f} kg")
    print(f"  总重量: {results.get('total_weight', 0):.2f} kg")


def demo_round_to_round():
    """演示圆对圆溜子设计"""
    print("\n" + "=" * 60)
    print("演示案例2: 圆对圆变径溜子")
    print("=" * 60)
    
    # 创建参数
    params = ChuteParameters(
        chute_type="round_to_round",
        height=2.0,  # 2米高
        plate_thickness=0.006,  # 6mm板厚
        material="304",  # 不锈钢
        inlet_shape="round",
        inlet_diameter=1.0,  # 进口直径1.0m
        outlet_shape="round",
        outlet_diameter=0.4,  # 出口直径0.4m
        inlet_flange_type="flat",
        inlet_flange_spec="60×5",
        outlet_flange_type="flat",
        outlet_flange_spec="50×4",
        offset_x=0.2,  # 偏心设计
        offset_y=0.15
    )
    
    print(f"设计参数:")
    print(f"  溜子类型: 圆对圆变径")
    print(f"  高度: {params.height} m")
    print(f"  板厚: {params.plate_thickness*1000} mm")
    print(f"  进口: φ{params.inlet_diameter} m")
    print(f"  出口: φ{params.outlet_diameter} m")
    print(f"  偏移: X={params.offset_x} m, Y={params.offset_y} m")
    print(f"  材料: {params.material} (不锈钢)")
    
    # 生成几何体和计算结果
    geometry_generator = ChuteGeometry(params)
    geometry_data = geometry_generator.generate_geometry()
    
    calculator = EngineeringCalculator(params, geometry_data)
    results = calculator.calculate_all()
    
    print(f"\n几何信息:")
    print(f"  顶点数量: {len(geometry_data['vertices'])}")
    print(f"  面片数量: {len(geometry_data['faces'])}")
    
    print(f"\n工程量计算结果:")
    print(f"  总表面积: {results.get('total_surface_area', 0):.3f} m²")
    print(f"  主体重量: {results.get('main_body_weight', 0):.2f} kg")
    print(f"  进口法兰: {results.get('inlet_flange_length', 0):.3f} m, {results.get('inlet_flange_weight', 0):.2f} kg")
    print(f"  出口法兰: {results.get('outlet_flange_length', 0):.3f} m, {results.get('outlet_flange_weight', 0):.2f} kg")
    print(f"  总重量: {results.get('total_weight', 0):.2f} kg")


def demo_square_to_square():
    """演示方对方溜子设计"""
    print("\n" + "=" * 60)
    print("演示案例3: 方对方变径溜子")
    print("=" * 60)
    
    # 创建参数
    params = ChuteParameters(
        chute_type="square_to_square",
        height=1.0,  # 1米高
        plate_thickness=0.010,  # 10mm板厚
        material="Q345",
        inlet_shape="square",
        inlet_width=1.5,  # 进口1.5m×1.2m
        inlet_length=1.2,
        outlet_shape="square",
        outlet_width=0.8,  # 出口0.8m×0.6m
        outlet_length=0.6,
        inlet_flange_type="angle",
        inlet_flange_spec="L63×5",
        outlet_flange_type="angle",
        outlet_flange_spec="L50×4",
        offset_x=0.0,  # 同心设计
        offset_y=0.0
    )
    
    print(f"设计参数:")
    print(f"  溜子类型: 方对方变径")
    print(f"  高度: {params.height} m")
    print(f"  板厚: {params.plate_thickness*1000} mm")
    print(f"  进口: {params.inlet_width}×{params.inlet_length} m")
    print(f"  出口: {params.outlet_width}×{params.outlet_length} m")
    print(f"  偏移: 同心设计")
    print(f"  材料: {params.material}")
    
    # 生成几何体和计算结果
    geometry_generator = ChuteGeometry(params)
    geometry_data = geometry_generator.generate_geometry()
    
    calculator = EngineeringCalculator(params, geometry_data)
    results = calculator.calculate_all()
    
    print(f"\n几何信息:")
    print(f"  顶点数量: {len(geometry_data['vertices'])}")
    print(f"  面片数量: {len(geometry_data['faces'])}")
    
    print(f"\n工程量计算结果:")
    print(f"  总表面积: {results.get('total_surface_area', 0):.3f} m²")
    print(f"  主体重量: {results.get('main_body_weight', 0):.2f} kg")
    print(f"  进口法兰: {results.get('inlet_flange_length', 0):.3f} m, {results.get('inlet_flange_weight', 0):.2f} kg")
    print(f"  出口法兰: {results.get('outlet_flange_length', 0):.3f} m, {results.get('outlet_flange_weight', 0):.2f} kg")
    print(f"  总重量: {results.get('total_weight', 0):.2f} kg")


def show_material_database():
    """显示材料数据库"""
    print("\n" + "=" * 60)
    print("材料数据库")
    print("=" * 60)
    
    config = AppConfig()
    
    print("支持的材料:")
    for key, value in config.MATERIALS.items():
        print(f"  {key}: {value['name']} (密度: {value['density']} kg/m³)")
    
    print(f"\n角钢规格 (共{len(config.ANGLE_STEEL_SPECS)}种):")
    for spec, data in list(config.ANGLE_STEEL_SPECS.items())[:8]:  # 显示前8种
        print(f"  {spec}: {data['weight']} kg/m")
    print("  ...")
    
    print(f"\n扁钢规格 (共{len(config.FLAT_STEEL_SPECS)}种):")
    for spec, data in list(config.FLAT_STEEL_SPECS.items())[:8]:  # 显示前8种
        print(f"  {spec}: {data['weight']} kg/m")
    print("  ...")


def main():
    """主演示函数"""
    print("溜子参数化设计与分析软件 - 功能演示")
    print("Chute Parametric Design Software - Demo")
    print("版本: 1.0.0")
    print("开发: AI Assistant")
    
    try:
        # 演示不同类型的溜子设计
        demo_square_to_round()
        demo_round_to_round()
        demo_square_to_square()
        
        # 显示材料数据库
        show_material_database()
        
        print("\n" + "=" * 60)
        print("演示完成！")
        print("=" * 60)
        print("\n软件特点:")
        print("✅ 支持多种溜子类型的参数化设计")
        print("✅ 实时三维可视化")
        print("✅ 精确的工程量计算")
        print("✅ 丰富的材料和规格数据库")
        print("✅ 用户友好的图形界面")
        
        print("\n启动图形界面:")
        print("运行 'python main.py' 启动完整的图形用户界面")
        
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
